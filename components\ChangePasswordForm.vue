<template>
  <BaseModal
    :is-open="isOpen"
    title="Đổi mật khẩu"
    size="lg"
    body-class="w-[452px] mx-auto"
    @close="closeModal"
    @open="onModalOpen"
  >
    <form @submit="onFormSubmit" class="space-y-6">
      <div class="space-y-6">
        <!-- Current Password -->
        <div class="flex flex-col gap-3">
          <label class="text-black text-base font-bold leading-[1.2]">
            Mật khẩu cũ
          </label>
          <div class="relative">
            <Field name="currentPassword" :type="showCurrentPassword ? 'text' : 'password'"
              :class="[
                'w-full h-[54px] px-[14px] pr-[50px] border rounded-lg text-base font-normal leading-[1.2] placeholder-[#919EAB] focus:outline-none',
                errors.currentPassword ? 'border-red-500 focus:border-red-500' : 'border-[#E9ECEE] focus:border-[#0066B3]'
              ]"
              placeholder="Nhậ<PERSON> mật cũ" />
            <button
              type="button"
              @click="toggleCurrentPassword"
              class="absolute right-3 top-1/2 transform -translate-y-1/2 text-[#919eab] hover:text-gray-700 focus:outline-none cursor-pointer"
            >
              <svg v-if="!showCurrentPassword" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
              </svg>
              <svg v-else class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
              </svg>
            </button>
          </div>
          <ErrorMessage name="currentPassword" class="text-xs text-red-600" />
        </div>

        <!-- New Password -->
        <div class="flex flex-col gap-3">
          <label class="text-black text-base font-bold leading-[1.2]">
            Mật khẩu mới
          </label>
          <div class="relative">
            <Field name="newPassword" :type="showNewPassword ? 'text' : 'password'"
              :class="[
                'w-full h-[54px] px-[14px] pr-[50px] border rounded-lg text-base font-normal leading-[1.2] placeholder-[#919EAB] focus:outline-none',
                errors.newPassword ? 'border-red-500 focus:border-red-500' : 'border-[#E9ECEE] focus:border-[#0066B3]'
              ]"
              placeholder="Nhập mật khẩu mới" />
            <button
              type="button"
              @click="toggleNewPassword"
              class="absolute right-3 top-1/2 transform -translate-y-1/2 text-[#919eab] hover:text-gray-700 focus:outline-none cursor-pointer"
            >
              <svg v-if="!showNewPassword" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
              </svg>
              <svg v-else class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
              </svg>
            </button>
          </div>

          <!-- Password Requirements -->
          <div class="flex flex-col gap-1">
            <div class="flex items-center gap-1">
              <div class="w-6 h-6 flex items-center justify-center">
                <svg class="w-3 h-3 text-[#919eab]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
              </div>
              <span class="text-black text-base font-normal leading-[1.2]">
                Tối thiểu 6 ký tự.
              </span>
            </div>
            <div class="flex gap-1">
              <div class="w-6 h-6 flex items-center justify-center flex-shrink-0">
                <svg class="w-3 h-3 text-[#919eab]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
              </div>
              <span
                class="text-black text-base font-normal leading-[1.2] h-10 flex items-center">
                Bao gồm chữ hoa, chữ thường, số và ký tự đặc biệt (ví dụ: @, #, $, %, &, v.v.)
              </span>
            </div>
          </div>

          <ErrorMessage name="newPassword" class="text-xs text-red-600" />
        </div>

        <!-- Confirm New Password -->
        <div class="flex flex-col gap-3">
          <label class="text-black text-base font-bold leading-[1.2]">
            Xác nhận mật khẩu mới
          </label>
          <div class="relative">
            <Field name="confirmPassword" :type="showConfirmPassword ? 'text' : 'password'"
              :class="[
                'w-full h-[54px] px-[14px] pr-[50px] border rounded-lg text-base font-normal leading-[1.2] placeholder-[#919EAB] focus:outline-none',
                errors.confirmPassword ? 'border-red-500 focus:border-red-500' : 'border-[#E9ECEE] focus:border-[#0066B3]'
              ]"
              placeholder="Nhập lại mật khẩu mới" />
            <button
              type="button"
              @click="toggleConfirmPassword"
              class="absolute right-3 top-1/2 transform -translate-y-1/2 text-[#919eab] hover:text-gray-700 focus:outline-none cursor-pointer"
            >
              <svg v-if="!showConfirmPassword" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
              </svg>
              <svg v-else class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
              </svg>
            </button>
          </div>
          <ErrorMessage name="confirmPassword" class="text-xs text-red-600" />
        </div>

        <!-- Submit Button -->
        <button type="submit" :disabled="isLoading"
          class="w-full h-[66px] bg-gradient-to-b from-[#0D68B2] to-[#0D68B2] rounded-[14.61px] shadow-[0px_10.96px_36.53px_0px_rgba(0,0,0,0.15)] flex items-center justify-center gap-[11.69px] px-[43.84px] py-[21.92px] disabled:opacity-50 disabled:cursor-not-allowed hover:from-[#0B5A9A] hover:to-[#0B5A9A] transition-colors">
          <span v-if="isLoading" class="flex items-center gap-2">
            <svg class="animate-spin h-5 w-5 text-[#F6F6F6]" xmlns="http://www.w3.org/2000/svg" fill="none"
              viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
              </path>
            </svg>
            <span class="text-[#F6F6F6] text-xl font-bold leading-[1.22] text-center">
              Đang xử lý...
            </span>
          </span>
          <span v-else
            class="text-[#F6F6F6] text-xl font-bold leading-[1.22] text-center">
            Đổi mật khẩu
          </span>
        </button>
      </div>
    </form>
  </BaseModal>
</template>

<script setup lang="ts">
import { Field, ErrorMessage, useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import * as z from 'zod'
import BaseModal from './BaseModal.vue'

interface Props {
  isOpen: boolean
}

interface Emits {
  close: []
  success: []
}

defineProps<Props>()
const emit = defineEmits<Emits>()

const isLoading = ref(false)

// State for password visibility
const showCurrentPassword = ref(false)
const showNewPassword = ref(false)
const showConfirmPassword = ref(false)

// Auto-hide password timers
let currentPasswordTimer: NodeJS.Timeout | null = null
let newPasswordTimer: NodeJS.Timeout | null = null
let confirmPasswordTimer: NodeJS.Timeout | null = null

// Functions to toggle password visibility with auto-hide
const toggleCurrentPassword = () => {
  showCurrentPassword.value = !showCurrentPassword.value

  // Clear existing timer
  if (currentPasswordTimer) {
    clearTimeout(currentPasswordTimer)
    currentPasswordTimer = null
  }

  // Set auto-hide timer if password is now visible
  if (showCurrentPassword.value) {
    currentPasswordTimer = setTimeout(() => {
      showCurrentPassword.value = false
      currentPasswordTimer = null
    }, 5000)
  }
}

const toggleNewPassword = () => {
  showNewPassword.value = !showNewPassword.value

  // Clear existing timer
  if (newPasswordTimer) {
    clearTimeout(newPasswordTimer)
    newPasswordTimer = null
  }

  // Set auto-hide timer if password is now visible
  if (showNewPassword.value) {
    newPasswordTimer = setTimeout(() => {
      showNewPassword.value = false
      newPasswordTimer = null
    }, 5000)
  }
}

const toggleConfirmPassword = () => {
  showConfirmPassword.value = !showConfirmPassword.value

  // Clear existing timer
  if (confirmPasswordTimer) {
    clearTimeout(confirmPasswordTimer)
    confirmPasswordTimer = null
  }

  // Set auto-hide timer if password is now visible
  if (showConfirmPassword.value) {
    confirmPasswordTimer = setTimeout(() => {
      showConfirmPassword.value = false
      confirmPasswordTimer = null
    }, 5000)
  }
}

// Toast composable
const { showSuccess, showError } = useToast()

// Định nghĩa Zod schema với TypeScript types
const zodSchema = z.object({
  currentPassword: z.string().min(1, 'Mật khẩu cũ là bắt buộc'),
  newPassword: z.string()
    .min(1, 'Mật khẩu mới là bắt buộc')
    .min(6, 'Mật khẩu phải có ít nhất 6 ký tự')
    .regex(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@#$%&])/,
      'Mật khẩu chưa đủ mạnh'
    )
    .refine((val) => !val.includes(' '), {
      message: 'Mật khẩu chưa đủ mạnh'
    }),
  confirmPassword: z.string().min(1, 'Xác nhận mật khẩu là bắt buộc')
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: 'Mật khẩu không trùng khớp',
  path: ['confirmPassword']
}).refine((data) => data.currentPassword !== data.newPassword, {
  message: 'Mật khẩu mới không được trùng mật khẩu cũ',
  path: ['newPassword']
})

// Type safety với Zod
type FormData = z.infer<typeof zodSchema>

// Chuyển đổi Zod schema thành vee-validate schema
const validationSchema = toTypedSchema(zodSchema)

// Sử dụng useForm composable
const { handleSubmit, errors, resetForm, setErrors } = useForm({
  validationSchema,
  initialValues: {
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  }
})

const closeModal = () => {
  // Clear all timers when closing modal
  if (currentPasswordTimer) {
    clearTimeout(currentPasswordTimer)
    currentPasswordTimer = null
  }
  if (newPasswordTimer) {
    clearTimeout(newPasswordTimer)
    newPasswordTimer = null
  }
  if (confirmPasswordTimer) {
    clearTimeout(confirmPasswordTimer)
    confirmPasswordTimer = null
  }

  // Reset password visibility
  showCurrentPassword.value = false
  showNewPassword.value = false
  showConfirmPassword.value = false

  emit('close')
  resetForm() // Reset form khi đóng modal
}

const onModalOpen = () => {
  // Reset form when modal opens
  resetForm()
}

// Hàm xử lý submit với type safety
const onSubmit = async (values: FormData) => {
  console.log('🔄 onSubmit called with values:', values)
  try {
    isLoading.value = true

    // Call API endpoint /v1/auth/reset-password
    const { post } = useApi()

    console.log('🌐 Calling API endpoint: /v1/auth/reset-password')

    const response = await post('/v1/auth/reset-password', {
      body: {
        oldPassword: values.currentPassword,
        newPassword: values.newPassword
      }
    })

    console.log('� API Response:', response)

    // Kiểm tra response thành công
    if (response && !response.error && response.data.code == 0) {
      // API call thành công
      console.log('✅ Password change successful')

      const successMessage = response.data?.message || 'Đổi mật khẩu thành công!'
      showSuccess(successMessage)
      emit('success')
      closeModal()
    } else {
      // API trả về lỗi
      console.log('❌ API returned error:', response)

      // Kiểm tra các loại lỗi cụ thể
      if (response.error && typeof response.error === 'object') {
        // Lỗi validation hoặc business logic
        const errorMessage = (response.error as any).message || response.error.toString()
        console.log('❌ Specific error message:', errorMessage)

        // Kiểm tra lỗi mật khẩu hiện tại không đúng
        if (errorMessage.toLowerCase().includes('password') &&
            (errorMessage.toLowerCase().includes('wrong') ||
             errorMessage.toLowerCase().includes('incorrect') ||
             errorMessage.toLowerCase().includes('sai') ||
             errorMessage.toLowerCase().includes('không đúng'))) {
          setErrors({
            currentPassword: 'Mật khẩu cũ không đúng'
          })
        } else {
          showError(errorMessage)
        }
      } else {
        // Lỗi chung
        const errorMessage = response.data.message ? String(response.data.message) : 'Có lỗi xảy ra khi đổi mật khẩu. Vui lòng thử lại.'
        showError(errorMessage)
      }
    }


  } catch (error: any) {
    console.error('💥 Catch block - Error changing password:', error)
    console.error('💥 Error details:', {
      message: error.message,
      stack: error.stack,
      name: error.name
    })
    showError('Có lỗi xảy ra khi đổi mật khẩu. Vui lòng thử lại.')
  } finally {
    console.log('🏁 Finally block - Setting loading to false')
    isLoading.value = false
  }
}

// Tạo handleSubmit function với onSubmit callback
const onFormSubmit = handleSubmit(onSubmit)

// Cleanup timers when component unmounts
onUnmounted(() => {
  if (currentPasswordTimer) {
    clearTimeout(currentPasswordTimer)
  }
  if (newPasswordTimer) {
    clearTimeout(newPasswordTimer)
  }
  if (confirmPasswordTimer) {
    clearTimeout(confirmPasswordTimer)
  }
})
</script>

<style scoped>
/* Custom focus styles */
.focus\:border-\[\#0066B3\]:focus {
  border-color: #0066B3;
}

/* Ensure proper font rendering */
.font-\[\'Helvetica_Neue\'\] {
  font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

.font-helvetica-neue {
  font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
}
</style>
