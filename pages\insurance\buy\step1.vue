<template>
  <div class="min-h-screen bg-white">
    <!-- Main Content -->
    <div class="w-full mx-auto px-4 lg:px-[390px] py-8">
      <!-- Steps Indicator -->
      <div class="mb-8">
        <StepsIndicator :current-step="1" />
      </div>

      <!-- Insurance Form -->
      <div class="flex justify-center">
        <InsuranceForm
          form-title="THÔNG TIN CHỦ XE"
          :show-owner-info="true"
          :show-vehicle-info="true"
          :show-insurance-period="true"
          :show-fee-info="false"
          :show-agreement="false"
          :show-back-button="false"
          submit-button-text="Tiếp tục"
          :initial-data="currentFormData"
          @submit="handleSubmit"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import StepsIndicator from '~/components/StepsIndicator.vue'
import InsuranceForm from '~/components/InsuranceForm.vue'

// Apply auth middleware
definePageMeta({
  middleware: 'auth'
})

// Set page title
useHead({
  title: '<PERSON><PERSON> bảo hiểm - Bước 1: <PERSON><PERSON> báo thông tin'
})

// Form data
const formData = ref({
  companyName: '',
  taxCode: '',
  companyAddress: '',
  phoneNumber: '',
  email: '',
  saveOwnerInfo: false,
  drive: '',
  licensePlate: '',
  numberOfSeats: '',
  chassisNumber: '',
  engineNumber: '',
  startDate: '',
  agreement: false
})

// Computed property to ensure reactivity
const currentFormData = computed(() => formData.value)

// Handle form submission
const handleSubmit = (data: any) => {
  // Save data to session storage or store
  if (import.meta.client) {
    sessionStorage.setItem('insuranceFormData', JSON.stringify(data))
  }

  // Navigate to step 2
  navigateTo('/insurance/buy/step2')
}

// Load saved data on mount
onMounted(() => {
  if (import.meta.client) {
    const savedData = sessionStorage.getItem('insuranceFormData')
    if (savedData) {
      try {
        const parsed = JSON.parse(savedData)
        formData.value = { ...formData.value, ...parsed }
      } catch (error) {
        console.error('Error parsing saved form data:', error)
      }
    }
  }
})
</script>

<style scoped>
/* Ensure proper layout */
.min-h-screen {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

main {
  flex: 1;
}
</style>
