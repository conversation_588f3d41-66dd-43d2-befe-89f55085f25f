<template>
  <div class="flex flex-col items-center justify-center w-full">
    <!-- Title Section -->
    <div class="flex flex-col items-center gap-[9px] mb-10">
      <div class="flex flex-col items-center">
        <h1
          class="text-[40px] text-center text-[#525252] leading-[1.2] font-bold">
          <PERSON><PERSON><PERSON> hiểm bắt buộc trách nhiệm dân sự của chủ xe ô tô
        </h1>
      </div>
      <div class="flex flex-col items-center w-full">
        <p class="text-[16px] text-center text-[#525252] leading-[1.2]">
          Vui lòng cung cấp đầy đủ các thông tin dưới đây để tiếp tục.
        </p>
      </div>
    </div>

    <!-- Steps Section -->
    <div class="relative flex justify-center">
      <!-- Step 1 -->
      <div class="box-border content-stretch relative">
        <div class="flex items-center justify-center">
          <div :class="[
            'box-border content-stretch flex flex-col gap-2.5 items-center justify-center px-4 py-3 relative rounded-[30px] shrink-0 size-[60px] transition-colors',
            currentStep >= 1
              ? 'bg-[#3079ff]'
              : ''
          ]">
            <div v-if="currentStep < 1"
              class="absolute border border-[#3079ff] border-solid inset-0 pointer-events-none rounded-[30px]" />
            <div :class="[
              'flex flex-col font-bold justify-center leading-[0] not-italic relative shrink-0 text-[28px] text-center text-nowrap transition-colors',
              currentStep >= 1 ? 'text-[#ffffff]' : 'text-[#3079ff]'
            ]">
              <p class="block leading-[1.2] whitespace-pre">1</p>
            </div>
          </div>
          <!-- Connector 1 -->
          <div class="w-[257px] h-0 relative">
            <div class="absolute bottom-0 left-0 right-0 top-[-2px]">
              <svg xmlns="http://www.w3.org/2000/svg" width="257" height="2" viewBox="0 0 257 2" fill="none"
                class="block max-w-none size-full">
                <line x1="8.74228e-08" y1="1" x2="257" y2="1.00002" stroke="#3079FF" stroke-width="2"
                  :stroke-dasharray="currentStep >= 2 ? '0' : '4 4'" />
              </svg>
            </div>
          </div>
        </div>
        <div :class="[
          'basis-0 flex flex-col justify-center leading-[0] min-h-px min-w-px not-italic relative shrink-0 text-[16px] text-center max-w-[143px] left-[-12%] pt-[15px] transition-colors',
          currentStep >= 1
            ? 'font-bold text-[#3079ff]'
            : 'text-[#000000]'
        ]">
          <p class="block leading-[1.2]">Khai báo thông tin mua bảo hiểm</p>
        </div>
      </div>


      <!-- Step 2 -->
      <div class="box-border content-stretch relative">
        <div class="flex items-center justify-center">
          <div :class="[
            'box-border content-stretch flex flex-col gap-2.5 items-center justify-center px-4 py-3 relative rounded-[30px] shrink-0 size-[60px] transition-colors',
            currentStep >= 2
              ? 'bg-[#3079ff]'
              : ''
          ]">
            <div v-if="currentStep < 2"
              class="absolute border border-[#3079ff] border-solid inset-0 pointer-events-none rounded-[30px]" />
            <div :class="[
              'flex flex-col font-bold justify-center leading-[0] not-italic relative shrink-0 text-[28px] text-center text-nowrap transition-colors',
              currentStep >= 2 ? 'text-[#ffffff]' : 'text-[#3079ff]'
            ]">
              <p class="block leading-[1.2] whitespace-pre">2</p>
            </div>
          </div>
          <!-- Connector 2 -->
          <div class="w-[253px] h-0 relative flex items-center justify-center">
            <div class="flex-none rotate-[180deg]">
              <div class="h-0 relative w-[253px]">
                <div class="absolute bottom-0 left-0 right-0 top-[-2px]">
                  <svg xmlns="http://www.w3.org/2000/svg" width="257" height="2" viewBox="0 0 257 2" fill="none"
                    class="block max-w-none size-full">
                    <line x1="8.74228e-08" y1="1" x2="257" y2="1.00002" stroke="#3079FF" stroke-width="2"
                      :stroke-dasharray="currentStep >= 3 ? '0' : '4 4'" />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div :class="[
          'basis-0 flex flex-col justify-center leading-[0] min-h-px min-w-px not-italic relative shrink-0 text-[16px] text-center max-w-[143px] left-[-12%] pt-[15px] transition-colors',
          currentStep >= 2
            ? 'font-bold text-[#3079ff]'
            : 'text-[#000000]'
        ]">
          <p class="block leading-[1.2]">Xác nhận thông tin</p>
        </div>
      </div>

      <!-- Step 3 -->
      <div class="relative">
        <div :class="[
          'w-[60px] h-[60px] rounded-[30px] flex items-center justify-center transition-colors',
          currentStep >= 3
            ? 'bg-[#3079ff]'
            : 'border border-[#3079ff] bg-white'
        ]">
          <span :class="[
            'text-[28px] font-bold leading-[1.2] transition-colors',
            currentStep >= 3 ? 'text-white' : 'text-[#3079ff]'
          ]">
            3
          </span>
        </div>
        <div class="flex flex-col items-center max-w-[143px] left-[-12%] relative pt-[15px]">
          <p :class="[
            'text-[16px] text-center leading-[1.2] transition-colors',
            currentStep >= 3
              ? 'text-[#3079ff] font-bold'
              : 'text-[#000000]'
          ]">
            Thanh toán
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  currentStep: number
}

withDefaults(defineProps<Props>(), {
  currentStep: 1
})
</script>

<style scoped>
/* Ensure smooth transitions */
.transition-colors {
  transition: background-color 0.3s ease, color 0.3s ease;
}
</style>
