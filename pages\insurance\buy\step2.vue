<template>
  <div class="min-h-screen bg-white">
    <!-- Main Content -->
    <div class="w-full mx-auto px-4 lg:px-[390px] py-8">
      <!-- Steps Indicator -->
      <div class="mb-8">
        <StepsIndicator :current-step="2" />
      </div>

      <!-- Insurance Form -->
      <div class="flex justify-center">
        <InsuranceForm
          form-title="THÔNG TIN CHỦ XE"
          :readonly="false"
          display-mode="summary"
          :show-owner-info="true"
          :show-vehicle-info="true"
          :show-insurance-period="true"
          :show-fee-info="true"
          :show-agreement="true"
          :show-back-button="false"
          submit-button-text="Tiếp tục"
          :initial-data="currentFormData"
          @submit="handleSubmit"
          @back="handleBack"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import StepsIndicator from '~/components/StepsIndicator.vue'
import InsuranceForm from '~/components/InsuranceForm.vue'

// Apply auth middleware
definePageMeta({
  middleware: 'auth'
})

// Set page title
useHead({
  title: '<PERSON><PERSON> b<PERSON><PERSON> hiểm - Bước 2: Xác nhận thông tin'
})

// Form data
const formData = ref({
  companyName: '',
  taxCode: '',
  companyAddress: '',
  phoneNumber: '',
  email: '',
  saveOwnerInfo: false,
  driverName: '',
  licensePlate: '',
  numberOfSeats: '',
  chassisNumber: '',
  engineNumber: '',
  startDate: '',
  agreement: false
})

// Computed property to ensure reactivity
const currentFormData = computed(() => formData.value)

// Toast composable
const { showSuccess, showError } = useToast()

// Handle form submission
const handleSubmit = async (data: any) => {
  try {
    console.log('Submitting insurance order:', data)

    // Prepare API request body theo format yêu cầu
    const requestBody = {
      ten_lai_xe: data.driverName,
      company_name: data.companyName,
      company_address: data.companyAddress,
      tax_number: data.taxCode,
      phone_number: data.phoneNumber,
      email_gcn: data.email,
      isUpdateInfo: true,
      bien_so_xe: data.licensePlate,
      so_cho_ngoi: parseInt(data.numberOfSeats) || 0,
      so_khung: data.chassisNumber || '',
      so_may: data.engineNumber || '',
      trong_tai: 15, // Trên 15 tấn (fixed value theo URD)
      loai_xe: 'Xe ô tô chở hàng (Xe tải)',
      md_su_dung: 'Kinh doanh vận tải',
      tg_bat_dau: new Date(data.startDate).getTime() / 1000, // Convert to timestamp
      tg_ket_thuc: new Date(data.startDate).getTime() / 1000 + (30 * 24 * 60 * 60) // +30 days
    }

    console.log('API Request Body:', requestBody)

    // Call API to create insurance order
    const api = useApi()
    const { data: apiData, error } = await api.post('/v1/orders/buy-insurance', {body: requestBody})

    if (error) {
      showError(apiData?.message || 'Có lỗi xảy ra khi tạo đơn bảo hiểm')
    }

    if (apiData?.code !== 0) {
      showError(apiData?.message || 'Có lỗi xảy ra khi tạo đơn bảo hiểm')
    }

    console.log('API Response:', apiData)

    // Save final data and order result
    if (import.meta.client) {
      sessionStorage.setItem('insuranceFormData', JSON.stringify(data))
      sessionStorage.setItem('insuranceOrderResult', JSON.stringify(apiData))
      sessionStorage.setItem('insuranceOrderStatus', apiData?.success ? 'success' : 'failed')
    }

    // Navigate to step 3
    if (apiData?.code === 0) {
      navigateTo(`/insurance/buy/result/${apiData?.data?.order_id}`)
    }
  } catch (error) {
    console.error('Error submitting insurance order:', error)

    // Set failed status and navigate to step 3
    if (import.meta.client) {
      sessionStorage.setItem('insuranceOrderStatus', 'failed')
      sessionStorage.setItem('insuranceOrderError', JSON.stringify({
        message: error instanceof Error ? error.message : 'Có lỗi xảy ra khi tạo đơn bảo hiểm'
      }))
    }
    // navigateTo('/insurance/buy/step3')
    navigateTo('/insurance/buy/result/${apiData?.data?.order_id}')  
  }
}

// Handle back button
const handleBack = () => {
  navigateTo('/insurance/buy/step1')
}

// Load saved data on mount
onMounted(() => {
  if (import.meta.client) {
    const savedData = sessionStorage.getItem('insuranceFormData')
    if (savedData) {
      try {
        const parsed = JSON.parse(savedData)
        formData.value = { ...formData.value, ...parsed }
      } catch (error) {
        console.error('Error parsing saved form data:', error)
        // If no data, redirect to step 1
        navigateTo('/insurance/buy/step1')
      }
    } else {
      // If no data, redirect to step 1
      navigateTo('/insurance/buy/step1')
    }
  }
})
</script>

<style scoped>
/* Ensure proper layout */
.min-h-screen {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

main {
  flex: 1;
}
</style>
