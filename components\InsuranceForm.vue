<template>
  <div class="bg-[#ffffff] border border-[#e9ecee] rounded-lg w-[1140px] mx-auto relative">
    <div class="absolute border border-[#e9ecee] border-solid inset-[-0.5px] pointer-events-none rounded-[8.5px]" />

    <!-- Form Content Container -->
    <div class="px-8 py-8">
      <!-- Form Title -->
      <div class="mb-8">
        <h1 class="text-[24px] font-bold text-[#000000] leading-[1.2]">{{ formTitle }}</h1>
      </div>

      <!-- Form Content -->
      <form @submit.prevent="handleSubmit" class="space-y-6">
        <!-- Owner Information Section -->
        <div v-if="showOwnerInfo" class="space-y-6">

          <!-- Summary Mode Display -->
          <div v-if="displayMode === 'summary'" class="space-y-4">
            <div class="flex justify-between items-start text-[16px] leading-[1.2]">
              <span class="text-[#000000]">Tên công ty</span>
              <span class="font-bold text-[#000000]">{{ formData.companyName }}</span>
            </div>
            <div class="flex justify-between items-start text-[16px] leading-[1.2]">
              <span class="text-[#000000]">Mã số thuế</span>
              <span class="font-bold text-[#000000]">{{ formData.taxCode }}</span>
            </div>
            <div class="flex justify-between items-start text-[16px] leading-[1.2]">
              <span class="text-[#000000]">Địa chỉ công ty</span>
              <span class="font-bold text-[#000000]">{{ formData.companyAddress }}</span>
            </div>
            <div class="flex justify-between items-start text-[16px] leading-[1.2]">
              <span class="text-[#000000]">Số điện thoại</span>
              <span class="font-bold text-[#000000]">{{ formData.phoneNumber }}</span>
            </div>
            <div class="flex justify-between items-start text-[16px] leading-[1.2]">
              <span class="text-[#000000]">Email nhận GCN bảo hiểm</span>
              <span class="font-bold text-[#000000]">{{ formData.email }}</span>
            </div>
          </div>

          <!-- Form Mode Display -->
          <div v-else class="space-y-6">
            <!-- Company Name -->
            <div class="flex flex-col gap-3">
              <label class="text-[16px] font-bold text-[#000000] leading-[1.2]">
                Tên công ty <span class="text-[#ff5656]">*</span>
              </label>
              <input v-model="formData.companyName" type="text" :disabled="readonly" :class="[
                'w-full px-3.5 py-0 h-[54px] border rounded-lg text-[16px] text-[#000000] leading-[1.2] transition-colors',
                readonly
                  ? 'bg-[#F8F9FA] border-[#E9ECEE] text-[#6C757D]'
                  : 'bg-[#ffffff] border-[#e9ecee] focus:border-[#3079FF] focus:outline-none hover:border-[#3079FF]'
              ]" placeholder="Vui lòng nhập tên công ty" maxlength="200" />
              <span v-if="errors.companyName" class="text-[#EE1C23] text-[12px]">{{ errors.companyName }}</span>
            </div>

            <!-- Tax Code -->
            <div class="flex flex-col gap-3">
              <label class="text-[16px] font-bold text-[#000000] leading-[1.2]">
                Mã số thuế <span class="text-[#ff5656]">*</span>
              </label>
              <input v-model="formData.taxCode" type="text" :disabled="readonly" :class="[
                'w-full px-3.5 py-0 h-[54px] border rounded-lg text-[16px] text-[#000000] leading-[1.2] transition-colors',
                readonly
                  ? 'bg-[#F8F9FA] border-[#E9ECEE] text-[#6C757D]'
                  : 'bg-[#ffffff] border-[#e9ecee] focus:border-[#3079FF] focus:outline-none hover:border-[#3079FF]'
              ]" placeholder="Vui lòng nhập mã số thuế" maxlength="200" />
              <span v-if="errors.taxCode" class="text-[#EE1C23] text-[12px]">{{ errors.taxCode }}</span>
            </div>

            <!-- Company Address -->
            <div class="flex flex-col gap-3">
              <label class="text-[16px] font-bold text-[#000000] leading-[1.2]">
                Địa chỉ công ty <span class="text-[#ff5656]">*</span>
              </label>
              <input v-model="formData.companyAddress" type="text" :disabled="readonly" :class="[
                'w-full px-3.5 py-0 h-[54px] border rounded-lg text-[16px] text-[#000000] leading-[1.2] transition-colors',
                readonly
                  ? 'bg-[#F8F9FA] border-[#E9ECEE] text-[#6C757D]'
                  : 'bg-[#ffffff] border-[#e9ecee] focus:border-[#3079FF] focus:outline-none hover:border-[#3079FF]'
              ]" placeholder="Vui lòng nhập địa chỉ công ty" />
              <span v-if="errors.companyAddress" class="text-[#EE1C23] text-[12px]">{{ errors.companyAddress }}</span>
            </div>

            <!-- Phone Number -->
            <div class="flex flex-col gap-3">
              <label class="text-[16px] font-bold text-[#000000] leading-[1.2]">
                Số điện thoại <span class="text-[#ff5656]">*</span>
              </label>
              <input v-model="formData.phoneNumber" type="tel" :disabled="readonly" :class="[
                'w-full px-3.5 py-0 h-[54px] border rounded-lg text-[16px] text-[#000000] leading-[1.2] transition-colors',
                readonly
                  ? 'bg-[#F8F9FA] border-[#E9ECEE] text-[#6C757D]'
                  : 'bg-[#ffffff] border-[#e9ecee] focus:border-[#3079FF] focus:outline-none hover:border-[#3079FF]'
              ]" placeholder="Vui lòng nhập số điện thoại" maxlength="10" pattern="[0-9]*" />
              <span v-if="errors.phoneNumber" class="text-[#EE1C23] text-[12px]">{{ errors.phoneNumber }}</span>
            </div>

            <!-- Email -->
            <div class="flex flex-col gap-3">
              <label class="text-[16px] font-bold text-[#000000] leading-[1.2]">
                Email nhận GCN bảo hiểm <span class="text-[#ff5656]">*</span>
              </label>
              <input v-model="formData.email" type="email" :disabled="readonly" :class="[
                'w-full px-3.5 py-0 h-[54px] border rounded-lg text-[16px] text-[#000000] leading-[1.2] transition-colors',
                readonly
                  ? 'bg-[#F8F9FA] border-[#E9ECEE] text-[#6C757D]'
                  : 'bg-[#ffffff] border-[#e9ecee] focus:border-[#3079FF] focus:outline-none hover:border-[#3079FF]'
              ]" placeholder="Vui lòng nhập email" />
              <span v-if="errors.email" class="text-[#EE1C23] text-[12px]">{{ errors.email }}</span>
            </div>

            <!-- Save Info Checkbox -->
            <div v-if="!readonly" class="flex items-center gap-3">
              <input v-model="formData.saveOwnerInfo" type="checkbox" id="saveOwnerInfo"
                class="w-5 h-5 text-[#3079FF] border-[#E9ECEE] rounded-[4px] focus:ring-[#3079FF] focus:ring-2" />
              <label for="saveOwnerInfo" class="text-[16px] text-[#333333] font-bold cursor-pointer">
                Lưu thông tin chủ xe
              </label>
            </div>
          </div>
        </div>

        <!-- Vehicle Information Section -->
        <div v-if="showVehicleInfo" class="space-y-6">
          <h2 class="text-[24px] font-bold text-[#6c757d] leading-[1.2] mb-6 uppercase">
            Thông tin xe
          </h2>

          <!-- Summary Mode Display -->
          <div v-if="displayMode === 'summary'" class="space-y-4">
            <div class="flex justify-between items-start text-[16px] leading-[1.2]">
              <span class="text-[#000000]">Tên lái xe</span>
              <span class="font-bold text-[#000000]">{{ formData.driverName }}</span>
            </div>
            <div class="flex justify-between items-start text-[16px] leading-[1.2]">
              <span class="text-[#000000]">Biển kiểm soát</span>
              <span class="font-bold text-[#000000]">{{ formData.licensePlate }}</span>
            </div>
            <div class="flex justify-between items-start text-[16px] leading-[1.2]">
              <span class="text-[#000000]">Số khung</span>
              <span class="font-bold text-[#000000]">{{ formData.chassisNumber || 'Không có' }}</span>
            </div>
            <div class="flex justify-between items-start text-[16px] leading-[1.2]">
              <span class="text-[#000000]">Số máy</span>
              <span class="font-bold text-[#000000]">{{ formData.engineNumber || 'Không có' }}</span>
            </div>
            <div class="flex justify-between items-start text-[16px] leading-[1.2]">
              <span class="text-[#000000]">Trọng tải</span>
              <span class="font-bold text-[#000000]">Trên 15 tấn</span>
            </div>
            <div class="flex justify-between items-start text-[16px] leading-[1.2]">
              <span class="text-[#000000]">Loại xe</span>
              <span class="font-bold text-[#000000]">Xe ô tô chở hàng (Xe tải)</span>
            </div>
            <div class="flex justify-between items-start text-[16px] leading-[1.2]">
              <span class="text-[#000000]">Số chỗ</span>
              <span class="font-bold text-[#000000]">2 chỗ</span>
            </div>
            <div class="flex justify-between items-start text-[16px] leading-[1.2]">
              <span class="text-[#000000]">Mục đích sử dụng</span>
              <span class="font-bold text-[#000000]">Kinh doanh vận tải</span>
            </div>
          </div>

          <!-- Form Mode Display -->
          <div v-else class="flex flex-col gap-6">
            <div class="flex gap-6">
              <!-- Number of Seats -->
              <div class="flex flex-col gap-3 w-1/2">
                <label class="text-[16px] font-bold text-[#000000] leading-[1.2]">
                  Tên lái xe <span class="text-[#ff5656]">*</span>
                </label>
                <input v-model="formData.driverName" type="text" :disabled="readonly" :class="[
                  'w-full px-3.5 py-0 h-[54px] border rounded-lg text-[16px] text-[#000000] leading-[1.2] transition-colors',
                  readonly
                    ? 'bg-[#F8F9FA] border-[#E9ECEE] text-[#6C757D]'
                    : 'bg-[#ffffff] border-[#e9ecee] focus:border-[#3079FF] focus:outline-none hover:border-[#3079FF]'
                ]" placeholder="Vui lòng nhập tên lái xe" maxlength="200" />
                <span v-if="errors.driverName" class="text-[#EE1C23] text-[12px]">{{ errors.driverName }}</span>
              </div>
              <!-- License Plate -->
              <div class="flex flex-col gap-3 w-1/2">
                <label class="text-[16px] font-bold text-[#000000] leading-[1.2]">
                  Biển kiểm soát <span class="text-[#ff5656]">*</span>
                </label>
                <input v-model="formData.licensePlate" type="text" :disabled="readonly" :class="[
                  'w-full px-3.5 py-0 h-[54px] border rounded-lg text-[16px] text-[#000000] leading-[1.2] transition-colors',
                  readonly
                    ? 'bg-[#F8F9FA] border-[#E9ECEE] text-[#6C757D]'
                    : 'bg-[#ffffff] border-[#e9ecee] focus:border-[#3079FF] focus:outline-none hover:border-[#3079FF]'
                ]" placeholder="Vui lòng nhập biển kiểm soát" maxlength="200" />
                <span v-if="errors.licensePlate" class="text-[#EE1C23] text-[12px]">{{ errors.licensePlate }}</span>
              </div>
            </div>
            <div class="flex gap-6">
              <!-- Chassis Number -->
              <div class="flex flex-col gap-3 w-1/2">
                <label class="text-[16px] font-bold text-[#000000] leading-[1.2]">Số khung
                  <span class="font-medium text-[#919EAB] italic"> (Không bắt buộc)</span>
                </label>
                <input v-model="formData.chassisNumber" type="text" :disabled="readonly" :class="[
                  'w-full px-3.5 py-0 h-[54px] border rounded-lg text-[16px] text-[#000000] leading-[1.2] transition-colors',
                  readonly
                    ? 'bg-[#F8F9FA] border-[#E9ECEE] text-[#6C757D]'
                    : 'bg-[#ffffff] border-[#e9ecee] focus:border-[#3079FF] focus:outline-none hover:border-[#3079FF]'
                ]" placeholder="Vui lòng nhập số khung" maxlength="200" />
              </div>

              <!-- Engine Number -->
              <div class="flex flex-col gap-3 w-1/2">
                <label class="text-[16px] font-bold text-[#000000] leading-[1.2]">Số máy
                  <span class="font-medium text-[#919EAB] italic"> (Không bắt buộc)</span>
                </label>
                <input v-model="formData.engineNumber" type="text" :disabled="readonly" :class="[
                  'w-full px-3.5 py-0 h-[54px] border rounded-lg text-[16px] text-[#000000] leading-[1.2] transition-colors',
                  readonly
                    ? 'bg-[#F8F9FA] border-[#E9ECEE] text-[#6C757D]'
                    : 'bg-[#ffffff] border-[#e9ecee] focus:border-[#3079FF] focus:outline-none hover:border-[#3079FF]'
                ]" placeholder="Vui lòng nhập số máy" maxlength="200" />
              </div>
            </div>
            <div class="flex gap-6">
              <!-- Weight Capacity (Disabled) -->
              <div class="flex flex-col gap-3 w-1/2">
                <label class="text-[16px] font-bold text-[#000000] leading-[1.2]">Trọng tải</label>
                <input type="text" value="Trên 15 tấn" disabled
                  class="w-full px-3.5 py-0 h-[54px] border rounded-lg text-[16px] bg-[#d4d4d5] border-[#e9ecee] text-[#000000] leading-[1.2]" />
              </div>

              <!-- Vehicle Type (Disabled) -->
              <div class="flex flex-col gap-3 w-1/2">
                <label class="text-[16px] font-bold text-[#000000] leading-[1.2]">Loại xe</label>
                <input type="text" value="Xe ô tô chở hàng (Xe tải)" disabled
                  class="w-full px-3.5 py-0 h-[54px] border rounded-lg text-[16px] bg-[#d4d4d5] border-[#e9ecee] text-[#000000] leading-[1.2]" />
              </div>
            </div>
            <div class="flex gap-6">
              <!-- numberOfSeats (Disabled) -->
              <div class="flex flex-col gap-3 w-1/2">
                <label class="text-[16px] font-bold text-[#000000] leading-[1.2]"> Số chỗ</label>
                <input type="text" value="2 chỗ" disabled
                  class="w-full px-3.5 py-0 h-[54px] border rounded-lg text-[16px] bg-[#d4d4d5] border-[#e9ecee] text-[#000000] leading-[1.2]" />
              </div>
              <!-- Purpose (Disabled) -->
              <div class="flex flex-col gap-3 w-1/2">
                <label class="text-[16px] font-bold text-[#000000] leading-[1.2]">Mục đích sử dụng</label>
                <input type="text" value="Kinh doanh vận tải" disabled
                  class="w-full px-3.5 py-0 h-[54px] border rounded-lg text-[16px] bg-[#d4d4d5] border-[#e9ecee] text-[#000000] leading-[1.2]" />
              </div>
            </div>
          </div>
        </div>

        <!-- Insurance Period Section -->
        <div v-if="showInsurancePeriod" class="space-y-6">
          <h2 class="text-[24px] font-bold text-[#6c757d] leading-[1.2] mb-6 uppercase">
            Thời hạn bảo hiểm
          </h2>

          <!-- Summary Mode Display -->
          <div v-if="displayMode === 'summary'" class="space-y-4">
            <div class="flex justify-between items-start text-[16px] leading-[1.2]">
              <span class="text-[#000000]">Ngày bắt đầu</span>
              <span class="font-bold text-[#000000]">{{ formatDisplayDate(formData.startDate) }}</span>
            </div>
            <div class="flex justify-between items-start text-[16px] leading-[1.2]">
              <span class="text-[#000000]">Ngày kết thúc</span>
              <span class="font-bold text-[#000000]">{{ formattedEndDate }}</span>
            </div>
          </div>

          <!-- Form Mode Display -->
          <div v-else>
            <!-- Date Fields Container -->
            <div class="flex gap-5">
              <!-- Start Date -->
              <div class="flex-1 flex flex-col gap-3">
                <label class="text-[16px] font-bold text-[#000000] leading-[1.2]">
                  Ngày bắt đầu <span class="text-[#ff5656]">*</span>
                </label>
                <input v-model="formData.startDate" type="date" :disabled="readonly" :class="[
                  'w-full px-3.5 py-0 h-[54px] border rounded-lg text-[16px] text-[#000000] leading-[1.2] transition-colors',
                  readonly
                    ? 'bg-[#F8F9FA] border-[#E9ECEE] text-[#6C757D]'
                    : 'bg-[#ffffff] border-[#e9ecee] focus:border-[#3079FF] focus:outline-none hover:border-[#3079FF]'
                ]" />
                <span v-if="errors.startDate" class="text-[#EE1C23] text-[12px]">{{ errors.startDate }}</span>
              </div>

              <!-- End Date (Disabled) -->
              <div class="flex-1 flex flex-col gap-3">
                <label class="text-[16px] font-bold text-[#000000] leading-[1.2]">
                  Ngày kết thúc <span class="text-[#ff5656]">*</span>
                </label>
                <input :value="formattedEndDate" type="text" disabled
                  class="w-full px-3.5 py-0 h-[54px] border rounded-lg text-[16px] bg-[#d4d4d5] border-[#e9ecee] text-[#000000] leading-[1.2]" />
              </div>
            </div>
          </div>
        </div>

        <!-- Fee Information Section (for confirmation step) -->
        <div v-if="showFeeInfo" class="space-y-6">
          <h2 class="text-[24px] font-bold text-[#6c757d] leading-[1.2] mb-6 uppercase">
            Phí bảo hiểm
          </h2>

          <div class="space-y-4">
            <div class="flex justify-between items-start text-[16px] leading-[1.2]">
              <span class="text-[#000000]">Phí chưa VAT</span>
              <span class="font-bold text-[#000000]">266.666đ</span>
            </div>
            <div class="flex justify-between items-start text-[16px] leading-[1.2]">
              <span class="text-[#000000]">Thuế VAT</span>
              <span class="font-bold text-[#000000]">26.667đ</span>
            </div>
            <div class="flex justify-between items-start text-[16px] leading-[1.2]">
              <span class="text-[#000000]">Tổng phí (gồm VAT)</span>
              <span class="font-bold text-[#000000]">293.333đ</span>
            </div>
            <div class="flex justify-between items-start text-[24px] leading-[1.2] font-bold">
              <span class="text-[#000000]">Tổng phí thanh toán</span>
              <span class="text-[#3079ff]">293.333đ</span>
            </div>
          </div>
        </div>

        <!-- Agreement Checkbox (for confirmation step) -->
        <div v-if="showAgreement" class="space-y-4">
          <div class="flex items-center gap-2">
            <div class="flex items-center justify-center p-[11px] rounded-full">
              <input v-model="formData.agreement" type="checkbox" id="agreement"
                class="w-[18px] h-[18px] text-[#49454f] border-2 border-[#49454f] rounded-sm focus:ring-[#49454f]" />
            </div>
            <label for="agreement" class="text-[16px] font-bold text-[#000000] leading-[1.2]">
              Tôi đồng ý đã đọc, hiểu các
              <a href="#" target="_blank" class="text-[#4f63ee] underline hover:no-underline">
                quy định Pháp luật về Bảo hiểm bắt buộc trách nhiệm dân sự của chủ xe ô tô
              </a>
            </label>
          </div>
        </div>

        <!-- Action Buttons -->
        <div v-if="!readonly" class="flex justify-end gap-4 pt-6">
          <button v-if="showBackButton" type="button" @click="$emit('back')"
            class="px-6 py-2 border border-[#E9ECEE] text-[#333333] rounded-md hover:bg-gray-50 transition-colors">
            Quay lại
          </button>
          <button type="submit" :disabled="submitDisabled" :class="[
            'w-full h-[52px] rounded-[14.613px] font-bold text-[20px] leading-normal transition-colors shadow-[0px_10.96px_36.532px_0px_rgba(0,0,0,0.15)]',
            submitDisabled
              ? 'bg-[#d4d4d5] text-[#f6f6f6] cursor-not-allowed'
              : 'bg-[#0d68b2] text-[#f6f6f6] hover:bg-[#0B5A9A]'
          ]">
            {{ submitButtonText }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from '~/stores/auth'

interface FormData {
  // Owner info
  companyName: string
  taxCode: string
  companyAddress: string
  phoneNumber: string
  email: string
  saveOwnerInfo: boolean

  // Vehicle info
  driverName: string
  licensePlate: string
  numberOfSeats: string
  chassisNumber: string
  engineNumber: string

  // Insurance period
  startDate: string

  // Agreement
  agreement: boolean
}

interface Props {
  formTitle: string
  readonly?: boolean
  displayMode?: 'form' | 'summary' // 'form' for input mode, 'summary' for readonly display
  showOwnerInfo?: boolean
  showVehicleInfo?: boolean
  showInsurancePeriod?: boolean
  showFeeInfo?: boolean
  showAgreement?: boolean
  showBackButton?: boolean
  submitButtonText?: string
  initialData?: Partial<FormData>
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false,
  displayMode: 'form',
  showOwnerInfo: true,
  showVehicleInfo: true,
  showInsurancePeriod: true,
  showFeeInfo: false,
  showAgreement: false,
  showBackButton: false,
  submitButtonText: 'Tiếp tục'
})

const emit = defineEmits<{
  submit: [data: FormData]
  back: []
}>()

// Auth store
const authStore = useAuthStore()

// Form data
const formData = ref<FormData>({
  companyName: authStore.user?.company_name || '', // Lấy tên công ty từ user đang đăng nhập
  taxCode: authStore.user?.tax_number || '', // Lấy mã số thuế từ user đang đăng nhập
  companyAddress: authStore.user?.company_address || '', // Lấy địa chỉ công ty từ user đang đăng nhập
  phoneNumber: authStore.user?.phone_number || '', // Lấy số điện thoại từ user đang đăng nhập
  email: authStore.user?.email_gcn || authStore.user?.username || '', // Lấy email mặc định từ user đang đăng nhập
  saveOwnerInfo: false,
  driverName: '',
  licensePlate: '',
  numberOfSeats: '',
  chassisNumber: '',
  engineNumber: '',
  startDate: '',
  agreement: false,
  ...props.initialData
})

// Errors
const errors = ref<Partial<Record<keyof FormData, string>>>({})

// Computed
const formattedEndDate = computed(() => {
  if (!formData.value.startDate) return ''
  const start = new Date(formData.value.startDate)
  const end = new Date(start.getTime() + 30 * 24 * 60 * 60 * 1000) // Add 30 days
  return end.toLocaleDateString('vi-VN', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  })
})

const formatDisplayDate = (dateString: string) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('vi-VN', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  })
}

const submitDisabled = computed(() => {
  if (props.showAgreement) {
    return !formData.value.agreement
  }
  return false
})

// Methods
const validateForm = (): boolean => {
  errors.value = {}
  let isValid = true

  if (props.showOwnerInfo) {
    if (!formData.value.companyName.trim()) {
      errors.value.companyName = 'Vui lòng nhập tên công ty'
      isValid = false
    }
    if (!formData.value.taxCode.trim()) {
      errors.value.taxCode = 'Vui lòng nhập mã số thuế'
      isValid = false
    }
    if (!formData.value.companyAddress.trim()) {
      errors.value.companyAddress = 'Vui lòng nhập địa chỉ công ty'
      isValid = false
    }
    if (!formData.value.phoneNumber.trim()) {
      errors.value.phoneNumber = 'Vui lòng nhập số điện thoại'
      isValid = false
    } else if (!/^\d{10}$/.test(formData.value.phoneNumber)) {
      errors.value.phoneNumber = 'Số điện thoại phải có 10 chữ số'
      isValid = false
    }
    if (!formData.value.email.trim()) {
      errors.value.email = 'Vui lòng nhập email'
      isValid = false
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.value.email)) {
      errors.value.email = 'Email không hợp lệ'
      isValid = false
    }
  }

  if (props.showVehicleInfo) {
    if (!formData.value.driverName.trim()) {
      errors.value.driverName = 'Vui lòng nhập tên lái xe'
      isValid = false
    }
    if (!formData.value.licensePlate.trim()) {
      errors.value.licensePlate = 'Vui lòng nhập biển kiểm soát'
      isValid = false
    }
    // if (!formData.value.numberOfSeats || !formData.value.numberOfSeats.trim()) {
    //   errors.value.numberOfSeats = 'Số chỗ là bắt buộc'
    //   isValid = false
    // } else if (!/^[1-9]$/.test(formData.value.numberOfSeats.trim())) {
    //   errors.value.numberOfSeats = 'Số chỗ phải là số từ 1-9'
    //   isValid = false
    // }
  }

  if (props.showInsurancePeriod) {
    if (!formData.value.startDate) {
      errors.value.startDate = 'Ngày bắt đầu là bắt buộc'
      isValid = false
    }
  }

  return isValid
}

const handleSubmit = () => {
  if (validateForm()) {
    emit('submit', formData.value)
  }
}

// Watch for initialData changes
watch(() => props.initialData, (newData) => {
  if (newData) {
    formData.value = { ...formData.value, ...newData }
  }
}, { immediate: true, deep: true })

// Watch for auth user changes to update form data
watch(() => authStore.user, (user) => {
  if (user) {
    // Chỉ điền thông tin nếu các trường đang trống (không ghi đè dữ liệu đã nhập)
    if (!formData.value.companyName) formData.value.companyName = user.company_name || ''
    if (!formData.value.taxCode) formData.value.taxCode = user.tax_number || ''
    if (!formData.value.companyAddress) formData.value.companyAddress = user.company_address || ''
    if (!formData.value.phoneNumber) formData.value.phoneNumber = user.phone_number || ''
    if (!formData.value.email) formData.value.email = user.email_gcn || user.username ||''
  }
}, { immediate: true })

// Initialize form data with current date
onMounted(() => {
  if (!formData.value.startDate) {
    const now = new Date()
    formData.value.startDate = now.toISOString().slice(0, 10) // YYYY-MM-DD format
  }
})
</script>

<style scoped>
/* Custom input focus styles */
input:focus {
  box-shadow: 0 0 0 2px rgba(48, 121, 255, 0.1);
}

/* Custom checkbox styles */
input[type="checkbox"]:checked {
  background-color: #3079FF;
  border-color: #3079FF;
}

/* Input placeholder styling */
input::placeholder {
  color: #919eab;
}

/* Transition effects */
.transition-colors {
  transition: background-color 0.2s ease, border-color 0.2s ease, color 0.2s ease;
}
</style>
