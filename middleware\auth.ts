import { useAuthStore } from '~/stores/auth'

export default defineNuxtRouteMiddleware((to) => {
  // Chỉ chạy trên client side
  if (import.meta.server) return

  const authStore = useAuthStore()

  // Khởi tạo auth state từ localStorage nếu chưa có
  if (!authStore.isAuthenticated) {
    authStore.initAuth()
  }

  // Kiểm tra xem user đã đăng nhập chưa
  if (!authStore.isLoggedIn) {
    // Lưu URL hiện tại để redirect sau khi đăng nhập
    if (import.meta.client) {
      sessionStorage.setItem('redirectAfterLogin', to.fullPath)
    }

    // Redirect về trang chủ với thông báo cần đăng nhập
    return navigateTo('/?login=required')
  }
})